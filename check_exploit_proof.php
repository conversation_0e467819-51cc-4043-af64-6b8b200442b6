<?php
/**
 * 漏洞验证结果检查脚本
 */
echo "=== Phar 反序列化漏洞验证结果 ===\n\n";

$proofFiles = glob("phar_exploit_proof_*.tmp");

if (empty($proofFiles)) {
    echo "❌ 未发现漏洞利用证明文件\n";
    echo "可能的原因:\n";
    echo "1. 漏洞利用未成功\n";
    echo "2. 文件已被清理\n";
    echo "3. 权限不足无法创建文件\n";
} else {
    echo "🚨 发现 " . count($proofFiles) . " 个漏洞利用证明文件:\n\n";

    foreach ($proofFiles as $file) {
        echo "📁 文件: $file\n";
        echo "📅 创建时间: " . date("Y-m-d H:i:s", filemtime($file)) . "\n";
        echo "📏 文件大小: " . filesize($file) . " 字节\n";
        echo "📄 文件内容:\n";
        echo str_repeat("-", 60) . "\n";
        echo file_get_contents($file);
        echo str_repeat("-", 60) . "\n\n";
    }

    echo "⚠️  安全建议:\n";
    echo "1. 立即修复 res.php 中的 Phar 反序列化漏洞\n";
    echo "2. 实施严格的输入验证和协议过滤\n";
    echo "3. 定期进行安全代码审计\n";
    echo "4. 清理所有证明文件\n";
}
?>