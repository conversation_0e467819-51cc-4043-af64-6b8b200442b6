<?php
/**
 * 检查 Phar 反序列化漏洞利用结果
 */

echo "=== Phar 反序列化漏洞利用结果检查 ===\n\n";

// 查找证明文件
$proofFiles = glob("phar_exploit_success_*.tmp");

if (empty($proofFiles)) {
    echo "❌ 未发现漏洞利用证明文件\n";
    echo "\n可能的原因:\n";
    echo "1. 漏洞尚未被触发\n";
    echo "2. res.php 已经修复了漏洞\n";
    echo "3. 文件权限问题\n";
    echo "4. Phar 文件路径不正确\n";
    
    echo "\n请确认:\n";
    echo "1. 已运行 generate_phar.php 生成 Phar 文件\n";
    echo "2. 通过浏览器访问了相应的 URL\n";
    echo "3. res.php 文件存在且可访问\n";
    
} else {
    echo "🚨 发现 " . count($proofFiles) . " 个漏洞利用证明文件!\n\n";
    echo "这证明了 Phar 反序列化漏洞被成功利用！\n\n";
    
    foreach ($proofFiles as $file) {
        echo "📁 证明文件: $file\n";
        echo "📅 创建时间: " . date("Y-m-d H:i:s", filemtime($file)) . "\n";
        echo "📏 文件大小: " . filesize($file) . " 字节\n";
        echo "📄 文件内容:\n";
        echo str_repeat("-", 60) . "\n";
        echo file_get_contents($file);
        echo str_repeat("-", 60) . "\n\n";
    }
    
    echo "⚠️  安全影响分析:\n";
    echo "1. ✅ 确认存在 Phar 反序列化漏洞\n";
    echo "2. ✅ 攻击者可以执行任意 PHP 代码\n";
    echo "3. ✅ 可能导致服务器完全被控制\n";
    echo "4. ✅ 敏感数据可能被窃取或篡改\n";
    
    echo "\n🔧 修复建议:\n";
    echo "1. 立即在 res.php 中添加协议过滤\n";
    echo "2. 使用 realpath() 验证文件路径\n";
    echo "3. 限制文件访问在指定目录内\n";
    echo "4. 考虑禁用 phar 流包装器\n";
    
    echo "\n🧹 清理选项:\n";
    echo "是否删除所有证明文件? (y/n): ";
    
    if (php_sapi_name() === 'cli') {
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) === 'y' || strtolower($input) === 'yes') {
            foreach ($proofFiles as $file) {
                unlink($file);
                echo "🗑️  已删除: $file\n";
            }
            echo "\n✅ 所有证明文件已清理\n";
        } else {
            echo "\n⚠️  证明文件保留，请手动清理\n";
        }
    } else {
        echo "\n⚠️  Web 环境下运行，请手动删除证明文件\n";
    }
}

// 检查生成的 Phar 文件
echo "\n=== 检查生成的 Phar 文件 ===\n";
$pharFiles = glob("uploads/*.jpg") + glob("uploads/*.png") + glob("uploads/*.gif");

if (!empty($pharFiles)) {
    echo "📦 发现以下 Phar 文件:\n";
    foreach ($pharFiles as $file) {
        if (is_file($file)) {
            echo "  - $file (" . filesize($file) . " 字节)\n";
        }
    }
    
    echo "\n💡 测试 URL 示例:\n";
    foreach ($pharFiles as $file) {
        $pharPath = "phar://$file/fake.jpg";
        $url = "res.php?action=revsliderprestashop_show_image&img=" . urlencode($pharPath);
        echo "  - $url\n";
    }
} else {
    echo "❌ 未找到生成的 Phar 文件\n";
    echo "请先运行 generate_phar.php\n";
}

echo "\n=== 检查完成 ===\n";
?>
